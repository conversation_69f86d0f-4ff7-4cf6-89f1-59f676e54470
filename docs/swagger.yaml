basePath: /api
definitions:
  api.CAResponse:
    properties:
      address:
        type: string
      chain_type:
        type: string
      is_recognized:
        type: boolean
      tags:
        items:
          type: string
        type: array
      token_details:
        description: Changed to array
        items:
          $ref: '#/definitions/api.TokenDetailsResponse'
        type: array
      tweets:
        items:
          $ref: '#/definitions/api.TweetInfo'
        type: array
    type: object
  api.NoticeResponse:
    properties:
      is_business_data:
        type: boolean
      is_ecosystem_partnership:
        type: boolean
      is_industry_event:
        type: boolean
      is_product_update:
        type: boolean
      is_profit_opportunity:
        type: boolean
    type: object
  api.TokenDetailsResponse:
    properties:
      chain_id:
        type: string
      holder_count:
        type: integer
      logo_url:
        type: string
      market_cap_usd:
        type: number
      name:
        type: string
      pair_created_at:
        type: integer
      price_usd:
        type: number
      source:
        description: Added source field
        type: string
      symbol:
        type: string
      twitter_url:
        type: string
    type: object
  api.TweetInfo:
    properties:
      article_cover_url:
        type: string
      article_preview_text:
        type: string
      article_title:
        type: string
      bookmark_count:
        type: integer
      bullet_points: {}
      content_type:
        description: '"tweet" or "article"'
        type: string
      favorite_count:
        type: integer
      id:
        type: string
      images:
        items:
          type: string
        type: array
      notices:
        $ref: '#/definitions/api.NoticeResponse'
      published_at:
        type: integer
      reply_count:
        type: integer
      retweet_count:
        type: integer
      source_list_type:
        description: '"KOLs" or "Projects"'
        type: string
      tags:
        items:
          type: string
        type: array
      text:
        type: string
      user:
        $ref: '#/definitions/api.UserResponse'
      views_count:
        type: integer
    type: object
  api.TweetResponse:
    properties:
      article_cover_url:
        type: string
      article_preview_text:
        type: string
      article_title:
        type: string
      bookmark_count:
        type: integer
      bullet_points: {}
      content_type:
        description: '"tweet" or "article"'
        type: string
      contract_addresses:
        items:
          $ref: '#/definitions/api.CAResponse'
        type: array
      favorite_count:
        type: integer
      id:
        type: string
      images:
        items:
          type: string
        type: array
      notices:
        $ref: '#/definitions/api.NoticeResponse'
      published_at:
        type: integer
      reply_count:
        type: integer
      retweet_count:
        type: integer
      source_list_type:
        description: '"KOLs" or "Projects"'
        type: string
      tags:
        items:
          type: string
        type: array
      text:
        type: string
      user:
        $ref: '#/definitions/api.UserResponse'
      views_count:
        type: integer
    type: object
  api.UserResponse:
    properties:
      followers_count:
        type: integer
      id:
        type: string
      is_verified:
        type: boolean
      name:
        type: string
      profile_image_url:
        type: string
      screen_name:
        type: string
    type: object
host: localhost:8080
info:
  contact:
    email: <EMAIL>
    name: API Support
  description: API for the Real-Time Contract Address service
  license:
    name: Apache 2.0
    url: http://www.apache.org/licenses/LICENSE-2.0.html
  termsOfService: http://swagger.io/terms/
  title: Real-Time CA Service API
  version: "1.0"
paths:
  /health:
    get:
      consumes:
      - application/json
      description: Returns 200 OK if the service is healthy
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Health check endpoint
      tags:
      - system
  /list-tweets:
    get:
      consumes:
      - application/json
      description: Retrieves a list of tweets related to AI Agent with filtering options
      parameters:
      - description: 'Number of tweets to return (default: 20)'
        in: query
        name: limit
        type: integer
      - description: 'Offset for pagination (default: 0)'
        in: query
        name: offset
        type: integer
      - description: Filter by content type (tweet, article, ALL)
        in: query
        name: content_type
        type: string
      - description: Filter by source type (KOLs, Projects, ALL)
        in: query
        name: source_type
        type: string
      - description: Filter by notice_type
        in: query
        name: notice_type
        type: string
      - description: Filter by user_id
        in: query
        name: user_id
        type: string
      - description: Filter by user_name
        in: query
        name: user_name
        type: string
      - collectionFormat: csv
        description: Filter by tags (comma-separated, e.g. 'tag1,tag2')
        in: query
        items:
          type: string
        name: tags
        type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/api.TweetResponse'
            type: array
        "500":
          description: Internal Server Error
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Get AI Agent related tweets
      tags:
      - tweets
  /recognized-ca/{address}/{chain_id}:
    get:
      consumes:
      - application/json
      description: Retrieves a specific recognized contract address by its address
        and chain ID
      parameters:
      - description: Contract address
        in: path
        name: address
        required: true
        type: string
      - description: Chain ID
        in: path
        name: chain_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.CAResponse'
        "404":
          description: Not Found
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Get a single recognized contract address
      tags:
      - contract-addresses
  /recognized-cas:
    get:
      consumes:
      - application/json
      description: Retrieves a list of recognized contract addresses
      parameters:
      - description: 'Number of CAs to return (default: 20)'
        in: query
        name: limit
        type: integer
      - description: 'Offset for pagination (default: 0)'
        in: query
        name: offset
        type: integer
      - collectionFormat: csv
        description: Filter by tags (comma-separated, e.g. 'tag1,tag2')
        in: query
        items:
          type: string
        name: tags
        type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/api.CAResponse'
            type: array
        "500":
          description: Internal Server Error
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Get recognized contract addresses
      tags:
      - contract-addresses
  /tweet/{tweet_id}:
    get:
      consumes:
      - application/json
      description: Retrieves a single tweet by its ID
      parameters:
      - description: Tweet ID
        in: path
        name: tweet_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.TweetResponse'
        "404":
          description: Not Found
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Get a tweet by ID
      tags:
      - tweets
  /tweets:
    get:
      consumes:
      - application/json
      description: Retrieves a list of tweets
      parameters:
      - description: 'Number of tweets to return (default: 20)'
        in: query
        name: limit
        type: integer
      - description: 'Offset for pagination (default: 0)'
        in: query
        name: offset
        type: integer
      - collectionFormat: csv
        description: Filter by tags (comma-separated, e.g. 'tag1,tag2')
        in: query
        items:
          type: string
        name: tags
        type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/api.TweetResponse'
            type: array
        "500":
          description: Internal Server Error
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Get tweets
      tags:
      - tweets
  /webhook/twitter:
    post:
      consumes:
      - application/json
      description: Processes webhook events from Twitter
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Handle Twitter webhook
      tags:
      - webhooks
securityDefinitions:
  ApiKeyAuth:
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"
