package services

import (
	"context"
	"fmt"
	"time"

	"github.com/bsm/redislock"
	"github.com/redis/go-redis/v9"
	"github.com/rs/zerolog/log"

	"real-time-ca-service/internal/config"
	"real-time-ca-service/internal/metrics"
)

// RedisService handles Redis operations and distributed locking
type RedisService struct {
	client *redis.Client
	locker *redislock.Client
	config config.RedisConfig
}

// NewRedisService creates a new Redis service
func NewRedisService(cfg config.RedisConfig) (*RedisService, error) {
	if !cfg.Enabled {
		log.Info().Msg("Redis is disabled in configuration")
		return &RedisService{config: cfg}, nil
	}

	// Create Redis client options
	opts := &redis.Options{
		Addr:         cfg.Address,
		Password:     cfg.Password,
		DB:           cfg.DB,
		PoolSize:     cfg.PoolSize,
		MinIdleConns: cfg.MinIdleConns,
		MaxRetries:   cfg.MaxRetries,
		DialTimeout:  time.Duration(cfg.DialTimeoutSec) * time.Second,
		ReadTimeout:  time.Duration(cfg.ReadTimeoutSec) * time.Second,
		WriteTimeout: time.Duration(cfg.WriteTimeoutSec) * time.Second,
		PoolTimeout:  time.Duration(cfg.PoolTimeoutSec) * time.Second,
		IdleTimeout:  time.Duration(cfg.IdleTimeoutSec) * time.Second,
	}

	// Create Redis client
	client := redis.NewClient(opts)

	// Test connection
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := client.Ping(ctx).Err(); err != nil {
		return nil, fmt.Errorf("failed to connect to Redis: %w", err)
	}

	// Create distributed lock client
	locker := redislock.New(client)

	log.Info().
		Str("address", cfg.Address).
		Int("db", cfg.DB).
		Int("pool_size", cfg.PoolSize).
		Msg("Successfully connected to Redis")

	return &RedisService{
		client: client,
		locker: locker,
		config: cfg,
	}, nil
}

// Close closes the Redis connection
func (r *RedisService) Close() error {
	if r.client != nil {
		return r.client.Close()
	}
	return nil
}

// IsEnabled returns whether Redis is enabled
func (r *RedisService) IsEnabled() bool {
	return r.config.Enabled && r.client != nil
}

// DistributedLock represents a distributed lock
type DistributedLock struct {
	lock    *redislock.Lock
	key     string
	service *RedisService
}

// ObtainLock attempts to obtain a distributed lock with the given key
func (r *RedisService) ObtainLock(ctx context.Context, key string) (*DistributedLock, error) {
	if !r.IsEnabled() {
		return nil, fmt.Errorf("Redis is not enabled")
	}

	lockKey := fmt.Sprintf("lock:%s", key)
	timeout := time.Duration(r.config.LockTimeoutSec) * time.Second
	
	start := time.Now()
	
	// Try to obtain the lock with retries
	var lock *redislock.Lock
	var err error
	
	for attempt := 0; attempt <= r.config.LockMaxRetries; attempt++ {
		lock, err = r.locker.Obtain(ctx, lockKey, timeout, nil)
		if err == nil {
			// Successfully obtained lock
			metrics.DistributedLocksTotal.WithLabelValues("obtained").Inc()
			
			log.Debug().
				Str("lock_key", lockKey).
				Dur("duration", time.Since(start)).
				Int("attempt", attempt+1).
				Msg("Successfully obtained distributed lock")
			
			return &DistributedLock{
				lock:    lock,
				key:     lockKey,
				service: r,
			}, nil
		}
		
		if err == redislock.ErrNotObtained {
			// Lock is held by another process, retry if we have attempts left
			if attempt < r.config.LockMaxRetries {
				retryDelay := time.Duration(r.config.LockRetryDelaySec) * time.Second
				log.Debug().
					Str("lock_key", lockKey).
					Int("attempt", attempt+1).
					Dur("retry_delay", retryDelay).
					Msg("Lock not obtained, retrying")
				
				select {
				case <-ctx.Done():
					metrics.DistributedLocksTotal.WithLabelValues("timeout").Inc()
					return nil, ctx.Err()
				case <-time.After(retryDelay):
					continue
				}
			}
		} else {
			// Other error occurred
			metrics.DistributedLocksTotal.WithLabelValues("error").Inc()
			return nil, fmt.Errorf("failed to obtain lock: %w", err)
		}
	}
	
	// Failed to obtain lock after all retries
	metrics.DistributedLocksTotal.WithLabelValues("failed").Inc()
	log.Warn().
		Str("lock_key", lockKey).
		Dur("duration", time.Since(start)).
		Int("max_retries", r.config.LockMaxRetries).
		Msg("Failed to obtain distributed lock after retries")
	
	return nil, fmt.Errorf("failed to obtain lock after %d retries", r.config.LockMaxRetries)
}

// Release releases the distributed lock
func (dl *DistributedLock) Release(ctx context.Context) error {
	if dl.lock == nil {
		return nil
	}
	
	start := time.Now()
	
	ok, err := dl.lock.Release(ctx)
	if err != nil {
		metrics.DistributedLocksTotal.WithLabelValues("release_error").Inc()
		log.Error().
			Err(err).
			Str("lock_key", dl.key).
			Msg("Failed to release distributed lock")
		return fmt.Errorf("failed to release lock: %w", err)
	}
	
	if !ok {
		metrics.DistributedLocksTotal.WithLabelValues("release_failed").Inc()
		log.Warn().
			Str("lock_key", dl.key).
			Msg("Lock was not released (may have expired)")
		return fmt.Errorf("lock was not released (may have expired)")
	}
	
	metrics.DistributedLocksTotal.WithLabelValues("released").Inc()
	log.Debug().
		Str("lock_key", dl.key).
		Dur("duration", time.Since(start)).
		Msg("Successfully released distributed lock")
	
	return nil
}

// TTL returns the remaining time-to-live of the lock
func (dl *DistributedLock) TTL(ctx context.Context) (time.Duration, error) {
	if dl.lock == nil {
		return 0, fmt.Errorf("lock is nil")
	}
	
	return dl.lock.TTL(ctx)
}

// Refresh extends the lock's expiration time
func (dl *DistributedLock) Refresh(ctx context.Context, ttl time.Duration) error {
	if dl.lock == nil {
		return fmt.Errorf("lock is nil")
	}
	
	return dl.lock.Refresh(ctx, ttl, nil)
}

// GetKey returns the lock key
func (dl *DistributedLock) GetKey() string {
	return dl.key
}

// WithLock executes a function while holding a distributed lock
func (r *RedisService) WithLock(ctx context.Context, key string, fn func() error) error {
	if !r.IsEnabled() {
		// If Redis is disabled, just execute the function without locking
		log.Debug().Str("key", key).Msg("Redis disabled, executing function without lock")
		return fn()
	}
	
	lock, err := r.ObtainLock(ctx, key)
	if err != nil {
		return fmt.Errorf("failed to obtain lock for key %s: %w", key, err)
	}
	
	defer func() {
		if releaseErr := lock.Release(ctx); releaseErr != nil {
			log.Error().
				Err(releaseErr).
				Str("lock_key", key).
				Msg("Failed to release lock in defer")
		}
	}()
	
	return fn()
}
