package db

import (
	"errors"
	"fmt"
	"sort"
	"strings"
	"time"

	"github.com/rs/zerolog/log"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/logger"

	"real-time-ca-service/internal/config"
)

// Connect establishes a connection to the database using GORM
func Connect(config config.DatabaseConfig) (*Database, error) {
	log.Debug().
		Str("host", config.Host).
		Int("port", config.Port).
		Str("user", config.User).
		Str("dbname", config.DBName).
		Str("sslmode", config.SSLMode).
		Msg("Connecting to database with GORM")

	dsn := fmt.Sprintf(
		"host=%s port=%d user=%s password=%s dbname=%s sslmode=%s",
		config.Host, config.Port, config.User, config.Password, config.DBName, config.SSLMode,
	)

	// Configure GORM
	gormConfig := &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent), // Use zerolog instead
	}

	// Open connection
	db, err := gorm.Open(postgres.Open(dsn), gormConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to open database connection: %w", err)
	}

	db = db.Debug()

	// Configure connection pool
	sqlDB, err := db.DB()
	if err != nil {
		return nil, fmt.Errorf("failed to get database connection: %w", err)
	}

	// Set connection pool settings
	sqlDB.SetMaxIdleConns(10)
	sqlDB.SetMaxOpenConns(100)
	sqlDB.SetConnMaxLifetime(time.Hour)

	// Test the connection
	if err := sqlDB.Ping(); err != nil {
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}

	log.Info().Msg("Successfully connected to database with GORM")
	return &Database{DB: db}, nil
}

// GetTwitterUserByScreenName retrieves a Twitter user by screen name
func (d *Database) GetTwitterUserByScreenName(screenName string) (*TwitterUser, error) {
	var user TwitterUser
	err := d.Where("screen_name = ?", screenName).First(&user).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

// SaveTwitterUser saves a Twitter user to the database
func (d *Database) SaveTwitterUser(user *TwitterUser) error {
	now := time.Now()
	if user.FetchedAt.IsZero() {
		user.FetchedAt = now
	}
	// BaseModel 中的 UpdatedAt 会自动由 GORM 处理

	// Use GORM's Upsert functionality
	result := d.Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "user_id"}},
		DoUpdates: clause.AssignmentColumns([]string{"screen_name", "name", "followers_count", "is_verified", "profile_image_url"}),
	}).Save(user)

	return result.Error
}

// SaveTweet saves a tweet to the database
func (d *Database) SaveTweet(tweet *Tweet) error {
	now := time.Now()
	if tweet.IngestedAt.IsZero() {
		tweet.IngestedAt = now
	}

	// Use GORM's Upsert functionality
	result := d.Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "tweet_id"}, {Name: "published_at"}},
		UpdateAll: true,
	}).Save(tweet)

	return result.Error
}

// AssociateTweetWithCA associates a tweet with a recognized contract address
func (d *Database) AssociateTweetWithCA(caAddress string, chainID string, tweet *Tweet, tagNames []string) error {
	now := time.Now()

	// Start a transaction
	return d.Transaction(func(tx *gorm.DB) error {
		// Check if the RecognizedCA already exists
		var recognizedCA RecognizedCA
		result := tx.Where("ca_address = ?", caAddress).First(&recognizedCA)

		if result.Error != nil {
			if !errors.Is(result.Error, gorm.ErrRecordNotFound) {
				return result.Error
			}

			// Create tags for the new CA
			tags, err := d.GetOrCreateTags(tagNames)
			if err != nil {
				return err
			}

			// RecognizedCA doesn't exist, create it
			recognizedCA = RecognizedCA{
				CAAddress:      caAddress,
				ChainType:      chainID,
				AddedAt:        now,
				LastTweetAt:    tweet.PublishedAt,
				ReferenceCount: 0, // Will be incremented later by IncrementCAReference
			}

			// Create the CA first
			if err := tx.Create(&recognizedCA).Error; err != nil {
				return err
			}

			// Now associate with tags
			if len(tags) > 0 {
				if err := d.AssociateCAWithTags(recognizedCA.ID, tags); err != nil {
					return err
				}
			}
		}

		// Now associate the RecognizedCA with the tweet using GORM's Append method
		if err := tx.Model(&Tweet{BaseModel: BaseModel{ID: tweet.ID}}).Association("ExtractedCAs").Append([]*RecognizedCA{{BaseModel: BaseModel{ID: recognizedCA.ID}}}); err != nil {
			return err
		}

		return nil
	})
}

// SaveRecognizedCA saves a recognized contract address to the database
func (d *Database) SaveRecognizedCA(ca *RecognizedCA) error {
	now := time.Now()
	if ca.AddedAt.IsZero() {
		ca.AddedAt = now
	}

	// Use GORM's Upsert functionality
	result := d.Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "ca_address"}},
		DoUpdates: clause.AssignmentColumns([]string{"chain_type", "token_name_hint"}),
	}).Save(ca)

	return result.Error
}

// GetOrCreateTag gets a tag by name or creates it if it doesn't exist
func (d *Database) GetOrCreateTag(tagName string) (*Tag, error) {
	var tag Tag

	// Try to find the tag first
	err := d.Where("name = ?", tagName).First(&tag).Error

	// If not found, create it
	if errors.Is(err, gorm.ErrRecordNotFound) {
		tag = Tag{
			Name:        tagName,
			Description: "",
			CreatedAt:   time.Now(),
		}

		if err := d.Create(&tag).Error; err != nil {
			return nil, err
		}
	} else if err != nil {
		return nil, err
	}

	return &tag, nil
}

// GetOrCreateTags gets or creates multiple tags by names
func (d *Database) GetOrCreateTags(tagNames []string) ([]Tag, error) {
	var tags []Tag

	for _, name := range tagNames {
		tag, err := d.GetOrCreateTag(name)
		if err != nil {
			return nil, err
		}
		tags = append(tags, *tag)
	}

	return tags, nil
}

// AssociateTweetWithTags associates a tweet with tags
func (d *Database) AssociateTweetWithTags(tweetID int64, tags []Tag) error {
	for _, tag := range tags {
		tweetTag := TweetTag{
			TweetID: tweetID,
			TagID:   tag.ID,
		}

		if err := d.Clauses(clause.OnConflict{DoNothing: true}).Create(&tweetTag).Error; err != nil {
			return err
		}
	}

	return nil
}

// AssociateCAWithTags associates a recognized CA with tags
func (d *Database) AssociateCAWithTags(caID int64, tags []Tag) error {
	for _, tag := range tags {
		caTag := RecognizedCATag{
			RecognizedCAID: caID,
			TagID:          tag.ID,
		}

		if err := d.Clauses(clause.OnConflict{DoNothing: true}).Create(&caTag).Error; err != nil {
			return err
		}
	}

	return nil
}

// SaveTokenDetails saves token details to the database
func (d *Database) SaveTokenDetails(token *TokenDetails) error {
	now := time.Now()
	token.LastUpdatedAt = now

	// Start a transaction
	err := d.Transaction(func(tx *gorm.DB) error {
		// Save token details with upsert
		if err := tx.Clauses(clause.OnConflict{
			// Use composite key of ca_address_fk and chain_id for upsert
			Columns:   []clause.Column{{Name: "ca_address_fk"}, {Name: "chain_id"}},
			UpdateAll: true,
		}).Save(token).Error; err != nil {
			return err
		}

		// Update the last checked time for the recognized CA
		if err := tx.Model(&RecognizedCA{}).
			Where("ca_address = ?", token.CAAddressFK).
			Update("last_checked_for_data_at", now).Error; err != nil {
			return err
		}

		return nil
	})

	return err
}

// GetTweets retrieves tweets with their associated data
// Only returns tweets that have at least one TokenDetails
func (d *Database) GetTweets(limit, offset int) ([]*Tweet, error) {
	var tweets []*Tweet

	// Query tweets with their users and associated recognized CAs using GORM's preloading
	// Join with recognized_cas and token_details to ensure at least one token_details exists
	err := d.Preload("User").
		Preload("ExtractedCAs").
		Preload("ExtractedCAs.TokenDetails").
		Preload("Tags").
		Joins("INNER JOIN tweet_contract_addresses ON tweets.id = tweet_contract_addresses.tweet_id").
		Joins("INNER JOIN recognized_cas ON tweet_contract_addresses.recognized_ca_id = recognized_cas.id").
		Joins("INNER JOIN token_details ON recognized_cas.ca_address = token_details.ca_address_fk").
		Where("contains_target_keyword = ?", true).
		Group("tweets.id"). // Group to avoid duplicates
		Order("published_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&tweets).Error

	if err != nil {
		return nil, err
	}

	return tweets, nil
}

// GetListTweetsRequest represents the parameters for retrieving tweets
type GetListTweetsRequest struct {
	Limit       int      `json:"limit"`
	Offset      int      `json:"offset"`
	ContentType string   `json:"content_type"`
	SourceType  string   `json:"source_type"`
	NoticeType  string   `json:"notice_type"`
	UserID      string   `json:"user_id"`
	UserName    string   `json:"user_name"`
	Tags        []string `json:"tags"`
}

// GetListTweets retrieves tweets related to AI Agent with filtering options
func (d *Database) GetListTweets(req GetListTweetsRequest) ([]*Tweet, error) {
	// If userName is provided, query userId from twitter user table and override userId
	if req.UserName != "" {
		user, err := d.GetTwitterUserByScreenName(req.UserName)
		if err == nil {
			// Override userId with the one found from twitter user table
			req.UserID = user.UserID
		} else {
			return nil, nil
		}
	}

	var tweets []*Tweet

	query := d.Preload("User").
		Preload("Tags").
		Where("ai_judgment = ?", "YES").
		Where("tweet_type <> ?", "reply")

	if req.ContentType != "" && req.ContentType != "ALL" {
		query = query.Where("content_type = ?", req.ContentType)
	}

	if req.SourceType != "" && req.SourceType != "ALL" {
		query = query.Where("source_list_type = ?", req.SourceType)
	}

	if req.UserID != "" {
		query = query.Where("user_id_fk = ?", req.UserID)
	}

	switch req.NoticeType {
	case "is_product_update":
		query = query.Where("is_product_update =?", true)
	case "is_business_data":
		query = query.Where("is_business_data =?", true)
	case "is_ecosystem_partnership":
		query = query.Where("is_ecosystem_partnership =?", true)
	case "is_profit_opportunity":
		query = query.Where("is_profit_opportunity =?", true)
	case "is_industry_event":
		query = query.Where("is_industry_event =?", true)
	default:
		// Do nothing, no specific notice type filter
	}

	if len(req.Tags) > 0 {
		// Join tweet_tags and tags tables
		query = query.Joins("INNER JOIN tweet_tags ON tweets.id = tweet_tags.tweet_id").
			Joins("INNER JOIN tags ON tweet_tags.tag_id = tags.id").
			Where("tags.name IN ?", req.Tags)
	}

	err := query.Order("published_at DESC").
		Limit(req.Limit).
		Offset(req.Offset).
		Find(&tweets).Error

	if err != nil {
		return nil, err
	}

	return tweets, nil
}

// GetTweetsByTags retrieves tweets with their associated data, filtered by tags
// If tags is empty, it returns all tweets that have at least one TokenDetails (similar to GetTweets)
func (d *Database) GetTweetsByTags(limit, offset int, tags []string) ([]*Tweet, error) {
	var tweets []*Tweet

	// Base query with preloads
	query := d.Preload("User").
		Preload("ExtractedCAs").
		Preload("ExtractedCAs.TokenDetails").
		Preload("Tags").
		// Join with recognized_cas and token_details to ensure at least one token_details exists
		Joins("INNER JOIN tweet_contract_addresses ON tweets.id = tweet_contract_addresses.tweet_id").
		Joins("INNER JOIN recognized_cas ON tweet_contract_addresses.recognized_ca_id = recognized_cas.id").
		Joins("INNER JOIN token_details ON recognized_cas.ca_address = token_details.ca_address_fk").
		Where("contains_target_keyword = ?", true)

	// If tags are provided, filter by them
	if len(tags) > 0 {
		// Join tweet_tags and tags tables
		query = query.Joins("INNER JOIN tweet_tags ON tweets.id = tweet_tags.tweet_id").
			Joins("INNER JOIN tags ON tweet_tags.tag_id = tags.id").
			Where("tags.name IN ?", tags)
	}

	// Group by tweets.id to avoid duplicates (needed in both cases now)
	query = query.Group("tweets.id")

	// Finalize the query with order, limit, offset
	err := query.Order("published_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&tweets).Error

	if err != nil {
		return nil, err
	}

	return tweets, nil
}

// GetTokenDetails retrieves all token details for a CA
func (d *Database) GetTokenDetails(caAddress string) ([]*TokenDetails, error) {
	var tokenDetails []*TokenDetails

	// Use GORM's Find method to find all token details for the CA
	err := d.Where("ca_address_fk = ?", caAddress).Find(&tokenDetails).Error

	if err != nil {
		return nil, err
	}

	return tokenDetails, nil
}

// GetTokenDetailsBySource retrieves token details for a CA from a specific source
func (d *Database) GetTokenDetailsBySource(caAddress string, source string) (*TokenDetails, error) {
	tokenDetails := &TokenDetails{}

	// Use GORM's First method to find the token details for the specific source
	err := d.Where("ca_address_fk = ? AND source = ?", caAddress, source).First(tokenDetails).Error

	if err != nil {
		return nil, err
	}

	return tokenDetails, nil
}

// IsCARecognized checks if a CA is recognized
func (d *Database) IsCARecognized(caAddress string) (bool, string, error) {
	var recognizedCA RecognizedCA

	// Use GORM to find the recognized CA
	err := d.Where("ca_address = ?", caAddress).First(&recognizedCA).Error

	// If record not found, return false
	if err == gorm.ErrRecordNotFound {
		return false, "", nil
	}

	if err != nil {
		return false, "", err
	}

	return true, recognizedCA.ChainType, nil
}

// GetRecognizedCAsForUpdate retrieves recognized CAs that need token data update
func (d *Database) GetRecognizedCAsForUpdate(limit int) ([]*RecognizedCA, error) {
	var recognizedCAs []*RecognizedCA

	// Use GORM to find CAs that need updating
	threshold := time.Now().Add(-100 * time.Minute)
	err := d.Where("last_checked_for_data_at IS NULL OR last_checked_for_data_at < ?", threshold).
		Order("last_tweet_at DESC").
		Order("last_checked_for_data_at NULLS FIRST").
		Limit(limit).
		Find(&recognizedCAs).Error

	if err != nil {
		return nil, err
	}

	return recognizedCAs, nil
}

// DeleteRecognizedCA deletes a recognized CA
func (d *Database) DeleteRecognizedCA(caAddress string) error {
	// Use GORM to delete the recognized CA
	return d.Delete(&RecognizedCA{}, "ca_address = ?", caAddress).Error
}

// IncrementCAReference increments the reference count for a recognized CA
func (d *Database) IncrementCAReference(caAddress string, chainID string, tokenNameHint string, tweetTime time.Time, tagNames []string) error {
	// Start a transaction
	return d.Transaction(func(tx *gorm.DB) error {
		// Try to find the existing recognized CA
		var recognizedCA RecognizedCA
		err := tx.Where("ca_address = ?", caAddress).First(&recognizedCA).Error

		var latestTime = tweetTime

		// If not found, create a new one with reference count 1
		if err == gorm.ErrRecordNotFound {
			// Create the tags first
			tags, err := d.GetOrCreateTags(tagNames)
			if err != nil {
				return err
			}

			newCA := &RecognizedCA{
				CAAddress:      caAddress,
				ChainType:      chainID,
				TokenNameHint:  tokenNameHint,
				LastTweetAt:    latestTime,
				AddedAt:        time.Now(),
				ReferenceCount: 1,
			}

			if err := tx.Create(newCA).Error; err != nil {
				return err
			}

			// Associate with tags
			if len(tags) > 0 {
				if err := d.AssociateCAWithTags(newCA.ID, tags); err != nil {
					return err
				}
			}

			return nil
		} else if err != nil {
			return err
		}

		if recognizedCA.LastTweetAt.After(latestTime) {
			latestTime = recognizedCA.LastTweetAt
		}

		// Update the reference count
		if err := tx.Model(&RecognizedCA{}).
			Where("ca_address = ?", caAddress).
			Update("last_tweet_at", latestTime).
			Update("reference_count", gorm.Expr("reference_count + 1")).Error; err != nil {
			return err
		}

		// Add new tags if provided
		if len(tagNames) > 0 {
			// Get or create the tags
			newTags, err := d.GetOrCreateTags(tagNames)
			if err != nil {
				return err
			}

			// Associate with CA
			if err := d.AssociateCAWithTags(recognizedCA.ID, newTags); err != nil {
				return err
			}
		}

		return nil
	})
}

// GetLatestTweetID gets the ID of the most recent tweet
func (d *Database) GetLatestTweetID() (string, error) {
	var tweet Tweet

	// Use GORM to find the most recent tweet
	err := d.Order("published_at DESC").Limit(1).First(&tweet).Error

	// If no tweets found, return empty string
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return "", nil
	}

	if err != nil {
		return "", err
	}

	return tweet.TweetID, nil
}

// TweetExists checks if a tweet ID already exists in the database
func (d *Database) TweetExists(tweetID string) (bool, error) {
	var tweet Tweet

	// Use GORM to check for the tweet
	err := d.Where("tweet_id = ?", tweetID).First(&tweet).Error

	// If record not found, return false
	if err == gorm.ErrRecordNotFound {
		return false, nil
	}

	if err != nil {
		return false, err
	}

	return true, nil
}

func (d *Database) TweetExistsWithTweet(tweetID string) (*Tweet, bool, error) {
	var tweet Tweet

	// Use GORM to check for the tweet
	err := d.Where("tweet_id = ?", tweetID).First(&tweet).Error

	// If record not found, return false
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, false, nil
	}

	if err != nil {
		return nil, false, err
	}

	return &tweet, true, nil
}

// GetRecognizedCAsWithTweets retrieves recognized CAs with their associated tweets, only including CAs with TokenDetails
func (d *Database) GetRecognizedCAsWithTweets(limit, offset int) ([]*RecognizedCA, error) {
	var recognizedCAs []*RecognizedCA

	// Query recognized CAs with their token details and tweets
	// Only include CAs that have at least one TokenDetails entry
	err := d.Preload("TokenDetails").
		Preload("Tweets.User").
		Where("EXISTS (SELECT 1 FROM token_details td WHERE td.ca_address_fk = recognized_cas.ca_address)").
		Order("last_tweet_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&recognizedCAs).Error

	if err != nil {
		return nil, err
	}

	for _, k := range recognizedCAs {
		// Sort each CA's Tweets by PublishedAt in descending order (newest first)
		if len(k.Tweets) > 1 {
			sort.Slice(k.Tweets, func(i, j int) bool {
				return k.Tweets[i].PublishedAt.After(k.Tweets[j].PublishedAt)
			})
		}
	}

	return recognizedCAs, nil
}

// GetRecognizedCAsByTags retrieves recognized CAs with their associated tweets, filtered by tags
// If tags is empty, it returns all CAs (same as GetRecognizedCAsWithTweets)
func (d *Database) GetRecognizedCAsByTags(limit, offset int, tags []string) ([]*RecognizedCA, error) {
	var recognizedCAs []*RecognizedCA

	// Base query with preloads
	query := d.Preload("TokenDetails").
		Preload("Tweets.User").
		Preload("Tags")

	// If tags are provided, filter by them
	if len(tags) > 0 {
		// We need to find CAs that have ANY of the requested tags (OR logic)
		// Build a single subquery with multiple OR conditions
		var tagConditions []string
		for _, tag := range tags {
			tagConditions = append(tagConditions, fmt.Sprintf("t.name = '%s'", tag))
		}

		// Combine all tag conditions with OR
		tagCondition := fmt.Sprintf(
			"EXISTS (SELECT 1 FROM recognized_ca_tags rct JOIN tags t ON rct.tag_id = t.id WHERE rct.recognized_ca_id = recognized_cas.id AND (%s))",
			strings.Join(tagConditions, " OR "),
		)
		query = query.Where(tagCondition)
	}

	// Only include CAs that have at least one TokenDetails entry
	query = query.Where("EXISTS (SELECT 1 FROM token_details td WHERE td.ca_address_fk = recognized_cas.ca_address)")

	// Finalize the query with order, limit, offset
	err := query.Order("last_tweet_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&recognizedCAs).Error

	if err != nil {
		return nil, err
	}

	for _, k := range recognizedCAs {
		// Sort each CA's Tweets by PublishedAt in descending order (newest first)
		if len(k.Tweets) > 1 {
			sort.Slice(k.Tweets, func(i, j int) bool {
				return k.Tweets[i].PublishedAt.After(k.Tweets[j].PublishedAt)
			})
		}
	}

	return recognizedCAs, nil
}

// GetRecognizedCAByAddressAndChainID retrieves a single recognized CA by address and chain ID
func (d *Database) GetRecognizedCAByAddressAndChainID(caAddress, chainID string) (*RecognizedCA, error) {
	var recognizedCA RecognizedCA

	// Query recognized CA with token details and tweets
	// Match RecognizedCA by address and ensure it has TokenDetails with the matching chain_id
	err := d.Preload("TokenDetails", "chain_id = ?", chainID).
		Preload("Tweets.User").
		Preload("Tags").
		Where("ca_address = ?", caAddress).
		Where("EXISTS (SELECT 1 FROM token_details td WHERE td.ca_address_fk = recognized_cas.ca_address AND td.chain_id = ?)", chainID).
		First(&recognizedCA).Error

	if err != nil {
		return nil, err
	}

	// Sort Tweets for CA in PublishedAt descending order (new first)
	if len(recognizedCA.Tweets) > 1 {
		sort.Slice(recognizedCA.Tweets, func(i, j int) bool {
			return recognizedCA.Tweets[i].PublishedAt.After(recognizedCA.Tweets[j].PublishedAt)
		})
	}

	return &recognizedCA, nil
}

// GetTweetByID retrieves a single tweet by its ID
func (d *Database) GetTweetByID(tweetID string) (*Tweet, error) {
	var tweet Tweet

	// Query tweet with user and associated contract addresses
	err := d.Preload("User").
		Preload("ExtractedCAs.TokenDetails").
		Preload("Tags").
		Where("tweet_id = ?", tweetID).
		First(&tweet).Error

	if err != nil {
		return nil, err
	}

	return &tweet, nil
}
