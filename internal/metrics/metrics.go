package metrics

import (
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
)

var (
	// TweetsProcessed counts the number of tweets processed
	TweetsProcessed = promauto.NewCounter(prometheus.CounterOpts{
		Name: "ca_service_tweets_processed_total",
		Help: "The total number of processed tweets",
	})

	// ContractAddressesExtracted counts the number of contract addresses extracted
	ContractAddressesExtracted = promauto.NewCounter(prometheus.CounterOpts{
		Name: "ca_service_contract_addresses_extracted_total",
		Help: "The total number of contract addresses extracted",
	})

	// RecognizedContractAddressesFound counts the number of recognized contract addresses found
	RecognizedContractAddressesFound = promauto.NewCounter(prometheus.CounterOpts{
		Name: "ca_service_recognized_contract_addresses_found_total",
		Help: "The total number of recognized contract addresses found",
	})

	// TokenDetailsUpdated counts the number of token details updated
	TokenDetailsUpdated = promauto.NewCounter(prometheus.CounterOpts{
		Name: "ca_service_token_details_updated_total",
		Help: "The total number of token details updated",
	})

	// APIRequestsTotal counts the total number of API requests
	APIRequestsTotal = promauto.NewCounterVec(prometheus.CounterOpts{
		Name: "ca_service_api_requests_total",
		Help: "The total number of API requests",
	}, []string{"endpoint", "method", "status"})

	// APIRequestDuration measures the duration of API requests
	APIRequestDuration = promauto.NewHistogramVec(prometheus.HistogramOpts{
		Name:    "ca_service_api_request_duration_seconds",
		Help:    "The duration of API requests in seconds",
		Buckets: prometheus.DefBuckets,
	}, []string{"endpoint", "method"})

	// ExternalAPIRequestsTotal counts the total number of external API requests
	ExternalAPIRequestsTotal = promauto.NewCounterVec(prometheus.CounterOpts{
		Name: "ca_service_external_api_requests_total",
		Help: "The total number of external API requests",
	}, []string{"api", "endpoint", "status"})

	// ExternalAPIRequestDuration measures the duration of external API requests
	ExternalAPIRequestDuration = promauto.NewHistogramVec(prometheus.HistogramOpts{
		Name:    "ca_service_external_api_request_duration_seconds",
		Help:    "The duration of external API requests in seconds",
		Buckets: prometheus.DefBuckets,
	}, []string{"api", "endpoint"})

	// DatabaseOperationsTotal counts the total number of database operations
	DatabaseOperationsTotal = promauto.NewCounterVec(prometheus.CounterOpts{
		Name: "ca_service_database_operations_total",
		Help: "The total number of database operations",
	}, []string{"operation", "table", "status"})

	// DatabaseOperationDuration measures the duration of database operations
	DatabaseOperationDuration = promauto.NewHistogramVec(prometheus.HistogramOpts{
		Name:    "ca_service_database_operation_duration_seconds",
		Help:    "The duration of database operations in seconds",
		Buckets: prometheus.DefBuckets,
	}, []string{"operation", "table"})

	// DistributedLocksTotal counts the total number of distributed lock operations
	DistributedLocksTotal = promauto.NewCounterVec(prometheus.CounterOpts{
		Name: "ca_service_distributed_locks_total",
		Help: "The total number of distributed lock operations",
	}, []string{"status"})

	// DistributedLockDuration measures the duration of distributed lock operations
	DistributedLockDuration = promauto.NewHistogramVec(prometheus.HistogramOpts{
		Name:    "ca_service_distributed_lock_duration_seconds",
		Help:    "The duration of distributed lock operations in seconds",
		Buckets: prometheus.DefBuckets,
	}, []string{"operation"})
)
