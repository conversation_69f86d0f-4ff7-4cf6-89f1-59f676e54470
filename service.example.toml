# Real-Time CA Service Configuration
# Copy this file to service.toml and update the values as needed

[server]
# Server address and port
address = ":8181"
# Server timeouts (in seconds)
read_timeout_sec = 30
write_timeout_sec = 30
shutdown_timeout_sec = 10
# Only start API server without background workers (true/false)
only_api = false
# Gin mode for the server (debug/release)
gin_mode = "debug"
# enable http server (true/false)
enable = false

[logging]
# Log level (debug, info, warn, error, fatal, panic)
level = "info"
# Pretty logging for development (true/false)
pretty = true

[metrics]
# Enable Prometheus metrics (true/false)
enabled = true
# Metrics endpoint path
path = "/metrics"

[database]
# PostgreSQL connection details
host = "localhost"
port = 5432
user = "postgres"
password = "postgres"
db_name = "ca_service"
ssl_mode = "disable"
# migrate_on_startup (true/false)
migrate_on_startup = true

[redis]
# Enable Redis for distributed locking and caching
enabled = true
# Redis server address (host:port)
address = "localhost:6379"
# Redis password (leave empty if no password)
password = ""
# Redis database number (0-15)
db = 0
# Connection pool settings
pool_size = 10
min_idle_conns = 5
max_retries = 3
# Timeout settings (in seconds)
dial_timeout_sec = 5
read_timeout_sec = 3
write_timeout_sec = 3
pool_timeout_sec = 4
idle_timeout_sec = 300
# Distributed lock settings
lock_timeout_sec = 30
lock_retry_delay_sec = 1
lock_max_retries = 3

[social_data]
# API key for SocialData.tools (REQUIRED)
api_key = "your_socialdata_api_key_here"
# Base URL for SocialData.tools API
base_url = "https://api.socialdata.tools"
# Keywords to search for in tweets (array of strings)
search_keywords = ["Ai agent CA"]
# Polling interval in seconds (how often to check for new tweets)
polling_interval_sec = 60
# Rate limiting - maximum requests per second to the API
requests_per_sec = 2 # 120 per minute
# Webhook configuration (for receiving push notifications instead of polling)
webhook_enabled = false
webhook_endpoint = "/api/webhook/twitter"
webhook_secret_key = ""
initial_lookback_hours = 24
# List of Twitter users to ignore when processing tweets
blacklisted_users = ["babyshark_agent", "realMOSSCoin"]
# Twitter lists to fetch tweets from (array of objects with id and type)
twitter_lists = [
    { id = "1928330914351960090", type = "KOLs" },
    { id = "1927997031173476588", type = "Projects" }
]
# Periodic update settings for tweets
periodic_update_enabled = false # Enable/disable periodic tweet updates
periodic_update_run_once_only = false # If true, only run update once at startup; if false, run periodically based on interval
periodic_update_interval_hours = 1 # Update interval in hours for recent tweets 0 is executed only once at startup, not in a loop
periodic_update_newer_than_hours = 48 # Tweets newer than this (in hours) will be updated more frequently
periodic_update_older_than_hours = 48 # Tweets older than this (in hours) will not be updated

[dex_screener]
# Base URL for DexScreener API
base_url = "https://api.dexscreener.com"
# Rate limiting - maximum requests per second to the API
requests_per_sec =  5 # 300 per minute
# Update interval in minutes (how often to update token data)
update_interval_min = 15
# Comma-separated list of supported blockchain networks
supported_chains = "ethereum,bsc,base,solana"

[moralis]
# API key for Moralis Web3 API (REQUIRED)
api_key = "your_moralis_api_key_here"
# Rate limiting - maximum requests per second to the API
requests_per_sec = 5 # 300 per minute

[ai]
# Enable AI features (true/false)
enabled = true
# OpenAI API key
api_key = "your_openai_api_key_here"
# AI model to use
model = "gpt-4.1-mini"
# Custom base URL for OpenAI API (leave empty for default)
base_url = ""
# Maximum number of retries for AI requests
max_retry = 3
# Rate limiting - maximum requests per second to the API
requests_per_sec = 9 # grok 10 rps

[telegram]
# Telegram Bot Token (REQUIRED if enabled)
bot_token = "your_telegram_bot_token_here"
# Telegram Chat ID to send messages to (REQUIRED if enabled)
chat_id = "your_telegram_chat_id_here"
# Enable Telegram notifications (true/false)
enabled = false
